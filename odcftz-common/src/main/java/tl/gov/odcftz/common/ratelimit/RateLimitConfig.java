package tl.gov.odcftz.common.ratelimit;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for rate limiting.
 */
@Data
@Component
@ConfigurationProperties(prefix = "rate-limit")
public class RateLimitConfig {

  /**
   * Whether rate limiting is enabled.
   */
  private boolean enabled = true;

  /**
   * Number of requests allowed per time period.
   */
  private int limitForPeriod = 10;

  /**
   * Time period duration in seconds.
   */
  private int limitRefreshPeriod = 1;

  /**
   * Timeout duration in milliseconds to wait for permission.
   */
  private int timeoutDuration = 0;

  /**
   * Maximum number of rate limiters to cache (one per IP).
   */
  private int maxCacheSize = 10000;

  /**
   * Cache expiration time in minutes for inactive rate limiters.
   */
  private int cacheExpirationMinutes = 60;
}
