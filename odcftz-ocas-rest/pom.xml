<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>tl.gov.odcftz.portfolio</groupId>
        <artifactId>odcftz-portfolio-backend</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>odcftz-ocas-rest</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>tl.gov.odcftz.portfolio</groupId>
            <artifactId>odcftz-portfolio-auth-service</artifactId>
        </dependency>

        <dependency>
            <groupId>tl.gov.odcftz.portfolio</groupId>
            <artifactId>odcftz-portfolio-service</artifactId>
        </dependency>

        <dependency>
            <groupId>tl.gov.odcftz.portfolio</groupId>
            <artifactId>odcftz-common</artifactId>
        </dependency>

    </dependencies>


    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <props>src/main/filters/local.properties</props>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <profile>
            <id>staging</id>
            <properties>
                <props>src/main/filters/staging.properties</props>
            </properties>
        </profile>

        <profile>
            <id>production</id>
            <properties>
                <props>src/main/filters/prod.properties</props>
            </properties>
        </profile>
    </profiles>

    <build>
        <!-- for using profiles-->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>application.yaml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>application.yaml</exclude>
                </excludes>
            </resource>
        </resources>
        <filters>
            <filter>${props}</filter>
        </filters>
        <!-- for using profiles-->

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                    <mainClass>tl.gov.odcftz.ocas.rest.OcasApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>