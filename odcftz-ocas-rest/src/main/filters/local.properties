# Environment name
app.environment=local

# Database configuration
# Connect to staging database using public URL
# Will need to add environment variables for this to work
# Probably either start a local postgres instance or add secrets to local env

db.url=*******************************************************
db.username=${PGUSER}
db.password=${PGPASSWORD}

flyway.enabled=false

# Security & Auth related
# Make sure to update to Kinde Application Settings
kinde.oauth2.domain=${KINDE_DOMAIN}
kinde.oauth2.client-id=${KINDE_CLIENT_ID}
kinde.oauth2.client-secret=${KINDE_CLIENT_SECRET}
kinde.oauth2.redirect-uri=http://localhost:8080/api/v1/auth/login/oauth2/code/kinde

kinde.oauth2.resourceserver.jwt.audiences=http://localhost:8080/api

kinde.client.post-login-redirect-uri=http://localhost:8080/dashboard
kinde.client.post-login-redirect-uri-path=/dashboard
kinde.client.post-logout-redirect-uri=http://localhost:8080/api/swagger-ui/index.html

kinde.m2m.client-id=${KINDE_M2M_CLIENT_ID}
kinde.m2m.client-secret=${KINDE_M2M_CLIENT_SECRET}
kinde.m2m.audiences=https://${KINDE_DOMAIN}/api
kinde.m2m.scopes=openid

security.csrf.enabled=false

# Cookie Configuration for Local Environment
# Less secure settings for local development
cookie.auth.http-only=true
cookie.auth.secure=false
cookie.auth.same-site=Lax
cookie.auth.path=/
cookie.auth.auth-cookie-name=ocas_access_token


# Enable Swagger for local environment
swagger.enabled=true

# Storage Configuration
storage.provider=r2

# Storage Buckets configuration for staging
storage.bucket.kyc=odcftz-portfolio-staging-kyc-documents
storage.bucket.deposits-fiat=odcftz-portfolio-staging-deposits-fiat
storage.bucket.general=odcftz-portfolio-staging-general-files

storage.r2.endpoint.url=https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com
storage.r2.access.key=${R2_ACCESS_KEY}
storage.r2.secret.key=${R2_SECRET_KEY}
storage.r2.region=${R2_REGION:auto}
storage.r2.path.style.access=true
