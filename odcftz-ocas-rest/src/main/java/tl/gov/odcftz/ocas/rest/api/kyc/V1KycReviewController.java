package tl.gov.odcftz.ocas.rest.api.kyc;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tl.gov.odcftz.common.file.dto.response.PresignedURLResponse;
import tl.gov.odcftz.common.rest.Paged;
import tl.gov.odcftz.common.rest.ResponseResult;
import tl.gov.odcftz.ocas.rest.api.kyc.assembler.KycReviewAssembler;
import tl.gov.odcftz.ocas.rest.common.auth.RequestOperator;
import tl.gov.odcftz.portfolio.core.kyc.application.command.dto.request.KycReviewRequest;
import tl.gov.odcftz.portfolio.core.kyc.application.query.dto.request.KycVerificationSearchRequest;
import tl.gov.odcftz.portfolio.core.kyc.application.query.dto.response.KycOperatorReasonDTO;
import tl.gov.odcftz.portfolio.core.kyc.application.query.dto.response.KycVerificationSearchResponse;
import tl.gov.odcftz.portfolio.core.kyc.domain.model.enums.KycOperatorAction;
import tl.gov.odcftz.portfolio.core.kyc.domain.model.enums.KycOperatorReason;

import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/kyc", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "KYC Review", description = "Admin APIs for KYC verification review and management")
@Slf4j
public class V1KycReviewController {

  @Resource private KycReviewAssembler kycReviewAssembler;

  @GetMapping("/verifications")
  public ResponseResult<Paged<KycVerificationSearchResponse>> searchKycVerifications(
      @Parameter(hidden = true) RequestOperator requestOperator,
      @ParameterObject @Valid @ModelAttribute KycVerificationSearchRequest request) {

    log.info("Received KYC search request: {}", request);
    Paged<KycVerificationSearchResponse> results =
        kycReviewAssembler.searchKycVerifications(request);
    return ResponseResult.success(results);
  }

  @GetMapping("/review/reasons")
  public ResponseResult<List<KycOperatorReasonDTO>> getReviewReasons(
      @Parameter(hidden = true) RequestOperator requestOperator,
      @NotNull KycOperatorAction action) {

    log.info("Received request for KYC operator reasons");
//    List<KycOperatorReasonDTO> reasons = kycReviewAssembler.getOperatorReasons();
    return ResponseResult.success(List.of(KycOperatorReasonDTO.from(KycOperatorReason.INVALID_DOCUMENT_TYPE)));
  }

  @GetMapping("/documents/url")
  public ResponseResult<PresignedURLResponse> getDocumentPresignedUrl(
      @Parameter(hidden = true) RequestOperator requestOperator,
      @NotBlank String fileKey) {

    log.info("Received presigned URL request for file key: {}", fileKey);
    PresignedURLResponse presignedUrl = kycReviewAssembler.generateDocumentPresignedUrl(fileKey);
    return ResponseResult.success(presignedUrl);
  }
   

  @PostMapping("/review")
  public ResponseResult<Void> reviewKyc(
      @Parameter(hidden = true) RequestOperator requestOperator,
      @RequestBody @Valid KycReviewRequest request) {
    return ResponseResult.success();
  }
}
