package tl.gov.odcftz.ocas.rest.api.kyc.assembler;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import tl.gov.odcftz.common.file.dto.request.PresignedURLRequest;
import tl.gov.odcftz.common.file.dto.response.PresignedURLResponse;
import tl.gov.odcftz.common.file.service.FileUploadService;
import tl.gov.odcftz.common.rest.Paged;
import tl.gov.odcftz.portfolio.core.common.sharedkernel.domain.file.Bucket;
import tl.gov.odcftz.portfolio.core.kyc.application.query.dto.request.KycVerificationSearchRequest;
import tl.gov.odcftz.portfolio.core.kyc.application.query.dto.response.KycVerificationSearchResponse;

import java.util.Collections;

@Component
@Slf4j
public class KycReviewAssembler {

  @Resource
  private FileUploadService fileUploadService;

//  @Resource
//  private KycReviewQueryService kycReviewQueryService;

  /**
   * Search KYC verifications with pagination
   *
   * @param request Search criteria
   * @return Paginated search results
   */
  public Paged<KycVerificationSearchResponse> searchKycVerifications(KycVerificationSearchRequest request) {
    log.info("Processing KYC search request: {}", request);

//    // Call the application service
//    Page<KycVerificationSearchResponse> page = kycReviewQueryService.searchKycVerifications(request);
//
//    // Convert Spring Data Page to our Paged wrapper
//    return Paged.of(
//        page.getContent(),
//        (int) page.getTotalElements(),
//        page.getNumber(),
//        page.getSize()
//    );
    return Paged.of(
        Collections.emptyList(),
        0,
        request.getPage(),
        request.getPageSize());
  }

  /**
   * Generate a presigned URL for accessing a KYC document
   *
   * @param fileKey The file key for the document
   * @return Presigned URL response with expiration
   */
  public PresignedURLResponse generateDocumentPresignedUrl(String fileKey) {
    log.info("Generating presigned URL for KYC document: {}", fileKey);
    
    // Create the presigned URL request with KYC bucket and file key
    PresignedURLRequest request = PresignedURLRequest.builder()
        .bucket(Bucket.KYC)
        .key(fileKey)
        .build();
    
    return fileUploadService.getPresignedURL(request);
  }
}