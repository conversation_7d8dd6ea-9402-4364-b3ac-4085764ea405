package tl.gov.odcftz.ocas.rest.config;

import jakarta.annotation.Resource;
import java.util.List;
import lombok.NonNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import tl.gov.odcftz.ocas.rest.common.auth.OperatorInitInterceptor;
import tl.gov.odcftz.ocas.rest.common.auth.RequestOperatorArgumentResolver;

/** Web configuration for the OCAS application. */
@Configuration
public class WebConfig implements WebMvcConfigurer {

  @Resource private OperatorInitInterceptor operatorInitInterceptor;
  @Resource private RequestOperatorArgumentResolver requestOperatorArgumentResolver;

  @Override
  public void addInterceptors(@NonNull InterceptorRegistry registry) {
    registry.addInterceptor(operatorInitInterceptor)
        .excludePathPatterns("/api/swagger-ui/**", "/api/v3/api-docs/**", "/api/swagger-ui.css", "/api/swagger-ui-bundle.js", "/api/swagger-ui-standalone-preset.js", "/api/swagger-ui-init.js");
  }

  @Override
  public void addArgumentResolvers(@NonNull List<HandlerMethodArgumentResolver> resolvers) {
    resolvers.add(requestOperatorArgumentResolver);
  }
}
