package tl.gov.odcftz.ocas.rest.config;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.ObjectPostProcessor;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.logout.CookieClearingLogoutHandler;
import org.springframework.security.web.authentication.session.NullAuthenticatedSessionStrategy;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import tl.gov.odcftz.auth.spring.security.AuthCookieClearingLogoutHandler;
import tl.gov.odcftz.auth.spring.security.CookieBasedBearerTokenResolver;
import tl.gov.odcftz.auth.spring.security.CookieBearerTokenRequestMatcher;
import tl.gov.odcftz.auth.spring.security.JwtAuthenticationOidcInitiatedLogoutHandler;
import tl.gov.odcftz.auth.spring.security.LogAccessDeniedExceptionHandler;
import tl.gov.odcftz.auth.spring.security.LogBasedAuthenticationEntryPoint;
import tl.gov.odcftz.auth.spring.security.ReturnJWTCookieOAuth2SuccessHandler;
import tl.gov.odcftz.auth.spring.security.SpaCsrfTokenRequestHandler;
import tl.gov.odcftz.auth.util.AuthCookieFactory;

@Configuration
@Slf4j
@EnableWebSecurity
public class WebSecurityConfig {

  @Resource private CookieBasedBearerTokenResolver cookieBasedBearerTokenResolver;

  @Resource private CookieBearerTokenRequestMatcher cookieJWTRequestMatcher;

  @Resource private SpaCsrfTokenRequestHandler spaCsrfTokenRequestHandler;

  @Resource private ReturnJWTCookieOAuth2SuccessHandler returnJWTCookieOAuth2SuccessHandler;

  @Resource private LogAccessDeniedExceptionHandler logAccessDeniedExceptionHandler;

  @Resource private LogBasedAuthenticationEntryPoint logBasedAuthenticationEntryPoint;

  @Resource
  private JwtAuthenticationOidcInitiatedLogoutHandler jwtAuthenticationOidcInitiatedLogoutHandler;

  @Resource private AuthCookieClearingLogoutHandler authCookieClearingLogoutHandler;

  @Value("${security.csrf.enabled:true}")
  private boolean csrfEnabled;

  @Bean
  @Order(1)
  public SecurityFilterChain authenticatedSecurityFilterChain(HttpSecurity http) throws Exception {
    // Only apply this filter chain to requests that has the Bearer Token in cookie i.e.
    // Authenticated
    http.securityMatcher(cookieJWTRequestMatcher);

    // Completely disable session management for JWT-only requests
    // This removes the SessionManagementFilter entirely from this filter chain
    http.sessionManagement(AbstractHttpConfigurer::disable);

    // Handled by NGINX
    http.cors(AbstractHttpConfigurer::disable);

    // Configure CSRF based on environment settings
    if (csrfEnabled) {
      log.info("CSRF protection is ENABLED for this environment");
      CookieCsrfTokenRepository cookieCsrfTokenRepository =
          CookieCsrfTokenRepository.withHttpOnlyFalse();
      // Update cookie domain to match your actual domain or use null for automatic domain detection
      cookieCsrfTokenRepository.setCookieCustomizer(AuthCookieFactory::setCsrfCookieConfig);

      http.csrf(
          csrf ->
              csrf.csrfTokenRepository(cookieCsrfTokenRepository)
                  .csrfTokenRequestHandler(spaCsrfTokenRequestHandler)
                  .withObjectPostProcessor(
                      new ObjectPostProcessor<CsrfFilter>() {
                        @Override
                        public <O extends CsrfFilter> O postProcess(O object) {
                          object.setRequireCsrfProtectionMatcher(CsrfFilter.DEFAULT_CSRF_MATCHER);
                          return object;
                        }
                      }));
    } else {
      log.info("CSRF protection is DISABLED for this environment");
      http.csrf(AbstractHttpConfigurer::disable);
    }

    http.authorizeHttpRequests(
        auth ->
            auth.requestMatchers("/api/v3/api-docs/**", "/api/swagger-ui/**")
                .permitAll()
                .anyRequest()
                .authenticated());

    http.oauth2ResourceServer(
        oauth2 ->
            oauth2
                .jwt(Customizer.withDefaults())
                .bearerTokenResolver(cookieBasedBearerTokenResolver));

    // Configure logout with our custom handler that clears cookies and redirects to OIDC logout
    // Note: This works even with filter ordering issues because our handler doesn't require
    // authentication context - it manually constructs the OIDC logout URL
    // GET logout is safe with SameSite=Strict cookies (production environment)
    http.logout(
        logout ->
            logout
                .logoutRequestMatcher(new AntPathRequestMatcher("/api/v1/auth/logout", "GET"))
                .addLogoutHandler(authCookieClearingLogoutHandler)
                .addLogoutHandler(new CookieClearingLogoutHandler("JSESSIONID"))
                .addLogoutHandler(jwtAuthenticationOidcInitiatedLogoutHandler));

    // Log and return HTTP 401
    http.exceptionHandling(
        exceptionHandler ->
            exceptionHandler
                .accessDeniedHandler(logAccessDeniedExceptionHandler)
                .authenticationEntryPoint(logBasedAuthenticationEntryPoint));

    return http.build();
  }

  @Bean
  @Order(2)
  public SecurityFilterChain oauthLoginSecurityFilterChain(HttpSecurity http) throws Exception {
    // http://localhost:8080/api/v1/auth/oauth2/authorization/kinde
    // http://localhost:8080/api/v1/auth/login/oauth2/code/kinde
    http.cors(AbstractHttpConfigurer::disable);

    // Configure CSRF based on environment settings
    if (csrfEnabled) {
      log.info("CSRF protection is ENABLED for this environment");
      CookieCsrfTokenRepository cookieCsrfTokenRepository =
          CookieCsrfTokenRepository.withHttpOnlyFalse();
      // Update cookie domain to match your actual domain or use null for automatic domain detection
      cookieCsrfTokenRepository.setCookieCustomizer(AuthCookieFactory::setCsrfCookieConfig);

      http.csrf(
          csrf ->
              csrf.csrfTokenRepository(cookieCsrfTokenRepository)
                  .csrfTokenRequestHandler(spaCsrfTokenRequestHandler)
                  .withObjectPostProcessor(
                      new ObjectPostProcessor<CsrfFilter>() {
                        @Override
                        public <O extends CsrfFilter> O postProcess(O object) {
                          object.setRequireCsrfProtectionMatcher(CsrfFilter.DEFAULT_CSRF_MATCHER);
                          return object;
                        }
                      }));
    } else {
      log.info("CSRF protection is DISABLED for this environment");
      http.csrf(AbstractHttpConfigurer::disable);
    }

    // Configure which requests require authentication
    http.authorizeHttpRequests(
        auth ->
            auth.requestMatchers("/api/v3/api-docs/**", "/api/swagger-ui/**")
                .permitAll()
                .requestMatchers("/api/v1/auth/login")
                .permitAll()
                .anyRequest()
                .authenticated() // All other requests require authentication
        );

    // OAuth2LoginAuthenticationFilter will create a new session for the oauth flow
    // It inherits AbstractAuthenticationProcessingFilter and uses
    // CompositeSessionAuthenticationStrategy by default for reducing session hijacking?
    // So upon authentication it will create a new session
    // Since we invalidate the oauth session immediately in the success handler, we can set this to
    // null
    http.sessionManagement(
        session ->
            session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .sessionAuthenticationStrategy(new NullAuthenticatedSessionStrategy()));

    // Configure OAuth2 login with custom endpoints
    http.oauth2Login(
        login ->
            login
                .authorizationEndpoint(authz -> authz.baseUri("/api/v1/auth/oauth2/authorization"))
                .redirectionEndpoint(
                    redirection -> redirection.baseUri("/api/v1/auth/login/oauth2/code/**"))
                .successHandler(returnJWTCookieOAuth2SuccessHandler));

    // Log and return HTTP 401
    http.exceptionHandling(
        exceptionHandler ->
            exceptionHandler
                .accessDeniedHandler(logAccessDeniedExceptionHandler)
                .authenticationEntryPoint(logBasedAuthenticationEntryPoint));

    return http.build();
  }
}
