package tl.gov.odcftz.portfolio.rest.config;

import jakarta.annotation.Resource;
import java.util.List;

import lombok.NonNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import tl.gov.odcftz.portfolio.rest.common.auth.n.RequestUserArgumentResolver;
import tl.gov.odcftz.portfolio.rest.common.auth.UserInitInterceptor;
import tl.gov.odcftz.portfolio.rest.common.auth.z.UserStatusInterceptor;

/** Web MVC configuration for the portfolio application. */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

  @Resource private RequestUserArgumentResolver requestUserArgumentResolver;
  @Resource private UserInitInterceptor userInitInterceptor;
  @Resource private UserStatusInterceptor userStatusInterceptor;

  @Override
  public void addInterceptors(@NonNull InterceptorRegistry registry) {
    // UserInitInterceptor must run first to initialize the user in request attributes
    registry.addInterceptor(userInitInterceptor);
    // UserStatusInterceptor runs after to check user status based on the initialized user
    registry.addInterceptor(userStatusInterceptor);
  }

  @Override
  public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
    resolvers.add(requestUserArgumentResolver);
  }
}
