spring:
  application:
    name: portfolio-rest

  # Internationalization configuration with multiple basenames
  messages:
    basename:
      - classpath:bundle/common-messages
      - classpath:bundle/messages
      - classpath:bundle/country-messages
    encoding: UTF-8
    fallback-to-system-locale: false
    always-use-message-format: false
    use-code-as-default-message: true

  datasource:
    url: @db.url@
    username: @db.username@
    password: @db.password@
    driver-class-name: org.postgresql.Driver
    hikari:
      connection-init-sql: SET timezone = 'Asia/Singapore'

  jackson:
    time-zone: Asia/Singapore
    date-format: yyyy-MM-dd'T'HH:mm:ss.SSSXXX

  flyway:
    enabled: @flyway.enabled@
    locations: classpath:db/migration
    schemas: portfolio
    table: flyway_schema_history

  # Multipart file upload configuration
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB

  security:
    oauth2:
      client:
        registration:
          kinde:
            provider: kinde
            client-id: @kinde.oauth2.client-id@
            client-secret: @kinde.oauth2.client-secret@
            scope: openid,email,profile
            authorization-grant-type: authorization_code
            redirect-uri: @kinde.oauth2.redirect-uri@
            domain: https://@kinde.oauth2.domain@

        provider:
          kinde:
            issuer-uri: https://@kinde.oauth2.domain@
            authorization-uri: https://@kinde.oauth2.domain@/oauth2/auth?audience=@kinde.oauth2.resourceserver.jwt.audiences@&is_use_auth_success_page=true


      resourceserver:
        jwt:
          issuer-uri: https://@kinde.oauth2.domain@
          audiences: @kinde.oauth2.resourceserver.jwt.audiences@

kinde:
  client:
    post-login-redirect-uri: @kinde.client.post-login-redirect-uri@
    post-login-redirect-uri-path: @kinde.client.post-login-redirect-uri-path@
    post-logout-redirect-uri: @kinde.client.post-logout-redirect-uri@
  m2m:
    domain: https://@kinde.oauth2.domain@
    client-id: @kinde.m2m.client-id@
    client-secret: @kinde.m2m.client-secret@
    audiences: @kinde.m2m.audiences@
    scopes: @kinde.m2m.scopes@

security:
  csrf:
    enabled: @security.csrf.enabled@
  auth-provider: kinde

# Cookie Configuration
cookie:
  auth:
    http-only: @cookie.auth.http-only@
    secure: @cookie.auth.secure@
    same-site: @cookie.auth.same-site@
    path: @cookie.auth.path@
    auth-cookie-name: @cookie.auth.auth-cookie-name@


mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto
      schema: portfolio
      # Logic delete field and values
#      logic-delete-field: deleted
#      logic-delete-value: 1
#      logic-not-delete-value: 0
  # type-handlers-package: tl.gov.odcftz.common.persistence.mybatis.type


# Swagger/OpenAPI configuration
springdoc:
  api-docs:
    enabled: @swagger.enabled@
    path: /api/v3/api-docs
  swagger-ui:
    enabled: @swagger.enabled@
    path: /api


# Storage Configuration
storage:
  provider: @storage.provider@
  buckets:
    kyc: @storage.bucket.kyc@
    general: @storage.bucket.general@
    deposits-fiat: @storage.bucket.deposits-fiat@

  # R2 Configuration
  r2:
    enabled: true
    endpoint-url: @storage.r2.endpoint.url@
    access-key: @storage.r2.access.key@
    secret-key: @storage.r2.secret.key@
    region: @storage.r2.region@
    path-style-access: @storage.r2.path.style.access@

#  # MinIO Configuration
#  minio:
#    enabled: false
#    endpoint-url: @storage.minio.endpoint.url@
#    access-key: @storage.minio.access.key@
#    secret-key: @storage.minio.secret.key@
#    region: @storage.minio.region@
#    path-style-access: @storage.minio.path.style.access@


logging:
  level:
    org.springframework.security: trace
    org.springframework.boot.web.servlet: trace
    com.baomidou.mybatisplus: TRACE
    org.apache.ibatis: TRACE
#    web: trace
#    org.openapitools: trace
#    org.openapitools.client: trace
#
