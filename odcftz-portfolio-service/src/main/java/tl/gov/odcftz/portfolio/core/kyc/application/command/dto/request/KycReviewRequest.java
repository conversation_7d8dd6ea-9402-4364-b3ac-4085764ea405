package tl.gov.odcftz.portfolio.core.kyc.application.command.dto.request;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import tl.gov.odcftz.portfolio.core.kyc.domain.model.enums.KycOperatorAction;
import tl.gov.odcftz.portfolio.core.kyc.domain.model.enums.KycOperatorReason;

public record KycReviewRequest(
    @NotNull Long submissionId,
    @NotNull KycOperatorAction action,
    @Nullable KycOperatorReason reason,
    @Nullable @Size(max = 500) String reasonExtra) {}
