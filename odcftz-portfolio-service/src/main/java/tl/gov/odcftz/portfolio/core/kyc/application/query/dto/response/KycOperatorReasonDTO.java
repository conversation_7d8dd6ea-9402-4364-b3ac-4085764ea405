package tl.gov.odcftz.portfolio.core.kyc.application.query.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import tl.gov.odcftz.common.i18n.I18nUtil;
import tl.gov.odcftz.portfolio.core.kyc.domain.model.enums.KycOperatorReason;

public record KycOperatorReasonDTO(
    KycOperatorReason reason, @Schema(description = "Localized text") String text) {

  public static KycOperatorReasonDTO from(KycOperatorReason reason) {
    return new KycOperatorReasonDTO(reason, getLocalizedText(reason));
  }

  /**
   * Get localized text for the reason code. Message key pattern:
   * kyc.operator.reason.{REASON_NAME_LOWERCASE}
   */
  private static String getLocalizedText(KycOperatorReason reason) {
    String messageKey = "kyc.operator.reason." + reason.name().toLowerCase();
    return I18nUtil.T(messageKey, reason.name()); // Fallback to enum name if key not found
  }
}
