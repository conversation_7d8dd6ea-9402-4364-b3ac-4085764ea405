package tl.gov.odcftz.portfolio.core.kyc.application.query.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import tl.gov.odcftz.portfolio.core.kyc.domain.model.enums.KycIdentityDocumentType;
import tl.gov.odcftz.portfolio.core.kyc.domain.model.enums.KycOperatorReason;
import tl.gov.odcftz.portfolio.core.kyc.domain.model.enums.KycStatus;

import java.time.OffsetDateTime;

@Data
@Builder
@Schema(description = "KYC verification submission details")
public class KycVerificationSearchResponse {

  // === User Information ===
  private String publicId;
  
  private String email;

  // === Personal Details from KYC ===
  private String firstName;
  
  private String lastName;
  
  private String nationality;
  
  private String countryOfResidence;

  // === Document Information ===
  private KycIdentityDocumentType identityDocumentType;
  
  private String documentFrontKey;
  
  private String documentBackKey;
  
  private String selfieKey;

  // === KYC Status and Timestamps ===
  private Long submissionId;
  
  private KycStatus status;
  
  private OffsetDateTime submissionDate;
  
  private OffsetDateTime lastUpdated;

  // === Operator Information (from audit) ===
  private String operatorName;
  
  private KycOperatorReason actionReason;

  // === Factory method for easy creation ===
  public static KycVerificationSearchResponse from(
      String publicId, String email, String firstName, String lastName,
      String nationality, String countryOfResidence,
      KycIdentityDocumentType identityDocumentType,
      String documentFrontKey, String documentBackKey, String selfieKey,
      KycStatus status, OffsetDateTime submissionDate, OffsetDateTime lastUpdated,
      String lastOperatorName, String actionReason) {

    return KycVerificationSearchResponse.builder()
        .publicId(publicId)
        .email(email)
        .firstName(firstName)
        .lastName(lastName)
        .nationality(nationality)
        .countryOfResidence(countryOfResidence)
        .identityDocumentType(identityDocumentType)
        .documentFrontKey(documentFrontKey)
        .documentBackKey(documentBackKey)
        .selfieKey(selfieKey)
        .submissionId(0L)
        .status(status)
        .submissionDate(submissionDate)
        .lastUpdated(lastUpdated)
        .operatorName(lastOperatorName)
//        .actionReason(actionReason)
        .build();
  }
}